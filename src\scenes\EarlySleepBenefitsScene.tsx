import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import TextType from '../components/reactbits/textanimations/TextType';
import Aurora from '../components/reactbits/backgrounds/Aurora';

const EarlySleepBenefitsScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 主标题滑动到左上角的动画 (90-150帧)
  const titleSlidePhase = interpolate(currentFrame, [90, 150], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 标题位置和大小变化
  const titleTranslateX = interpolate(titleSlidePhase, [0, 1], [0, -45], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleTranslateY = interpolate(titleSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleScale = interpolate(titleSlidePhase, [0, 1], [1, 0.4], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 标题淡出动画 (280帧开始淡出)
  const titleFadeOut = interpolate(currentFrame, [280, 300], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点动画 - 先居中显示，然后滑动到上方
  const corePointCenterPhase = interpolate(currentFrame, [150, 240], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点滑动到上方的动画 (240-300帧)
  const corePointSlidePhase = interpolate(currentFrame, [240, 300], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点位置和大小变化
  const corePointTranslateY = interpolate(corePointSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const corePointScale = interpolate(corePointSlidePhase, [0, 1], [1, 0.8], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个益处的依次出现 - 在核心观点滑动完成后开始
  const benefit1FadeIn = interpolate(currentFrame, [330, 360], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const benefit2FadeIn = interpolate(currentFrame, [390, 420], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const benefit3FadeIn = interpolate(currentFrame, [450, 480], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 结论动画
  const conclusionFadeIn = interpolate(currentFrame, [540, 570], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const conclusionScale = interpolate(currentFrame, [540, 570], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个要点在结论出现时淡出
  const benefitsFadeOut = interpolate(currentFrame, [540, 570], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="bg-black flex flex-col items-center justify-center text-white relative overflow-hidden">
      <div className="absolute inset-0 opacity-60">
        <Aurora
          colorStops={["#5227FF", "#7cff67", "#ff6b6b", "#4ecdc4"]}
          amplitude={1.2}
          blend={0.7}
          speed={0.8}
        />
      </div>

      {/* 主标题 - 先居中显示，然后滑动到上方 */}
      <div
        className="absolute z-20 w-full h-full flex justify-center items-center"
        style={{
          opacity: titleFadeOut,
          transform: `translate(${titleTranslateX}vw, ${titleTranslateY}vh) scale(${titleScale})`,
          transformOrigin: 'center center',
          transition: 'none',
        }}
      >
        <div className="text-center max-w-4xl px-8">
          <TextType
            text="早睡的确有益处，但并非唯一关键"
            className="text-6xl md:text-8xl lg:text-9xl font-semibold text-center leading-tight"
            useFrameBasedAnimation={true}
            startFrame={0}
            duration={90}
            showCursor={true}
            loop={false}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full">

        {/* 核心观点 - 先居中显示，然后滑动到上方 */}
        <div
          className="absolute z-15 w-full h-full flex justify-center items-center"
          style={{
            opacity: corePointCenterPhase * benefitsFadeOut,
            transform: `translate(0vw, ${corePointTranslateY}vh) scale(${corePointScale})`,
            transformOrigin: 'center center',
            transition: 'none',
          }}
        >
          <div className="text-center max-w-6xl px-4 overflow-visible">
            <div className="text-4xl md:text-5xl lg:text-6xl text-green-300 font-medium mb-4">
              早睡确实对身体有好处
            </div>
            <TextType
              text="尤其是和「昼夜节律」保持一致的时候——"
              className="text-xl md:text-5xl text-gray-300"
              useFrameBasedAnimation={true}
              startFrame={180}
              duration={80}
              showCursor={true}
              loop={false}
            />
          </div>
        </div>

        {/* 三个益处 */}
        <div
          className="flex-1 flex flex-col px-16 space-y-6"
          style={{
            marginTop: '40vh',
            opacity: benefitsFadeOut
          }}
        >
          {/* 益处1：褪黑素分泌 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-green-400/30"
            style={{ opacity: benefit1FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-green-200">
                1、褪黑素分泌：通常在晚上 9–11 点开始增加
              </p>
            </div>
          </div>

          {/* 益处2：学习和记忆 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-teal-400/30"
            style={{ opacity: benefit2FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-teal-200">
                2、学习和记忆：较早入睡的人专注力更高
              </p>
            </div>
          </div>

          {/* 益处3：心理健康 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/30"
            style={{ opacity: benefit3FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-blue-200">
                3、心理健康：长期熬夜会增加焦虑和抑郁风险
              </p>
            </div>
          </div>
        </div>

        {/* 结论说明 - 居中显示 */}
        <div
          className="absolute inset-0 flex items-center justify-center px-8 z-30"
          style={{
            opacity: conclusionFadeIn,
            transform: `scale(${conclusionScale})`,
          }}
        >
          <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-8 border border-green-400/30 max-w-4xl">
            <p className="text-6xl md:text-6xl font-semibold text-white text-center">
              所以，早睡确实对身体有好处，尤其是和"昼夜节律"保持一致的时候
            </p>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default EarlySleepBenefitsScene;