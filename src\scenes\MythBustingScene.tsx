import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import TextType from '../components/reactbits/textanimations/TextType';
import Aurora from '../components/reactbits/backgrounds/Aurora';

const MythBustingScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 主标题滑动到左上角的动画 (90-150帧)
  const titleSlidePhase = interpolate(currentFrame, [90, 150], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 标题位置和大小变化
  const titleTranslateX = interpolate(titleSlidePhase, [0, 1], [0, -45], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleTranslateY = interpolate(titleSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleScale = interpolate(titleSlidePhase, [0, 1], [1, 0.4], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 标题淡出动画 (280帧开始淡出)
  const titleFadeOut = interpolate(currentFrame, [280, 300], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点动画 - 先居中显示，然后滑动到上方
  const corePointCenterPhase = interpolate(currentFrame, [150, 240], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点滑动到上方的动画 (240-300帧)
  const corePointSlidePhase = interpolate(currentFrame, [240, 300], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点位置和大小变化
  const corePointTranslateY = interpolate(corePointSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const corePointScale = interpolate(corePointSlidePhase, [0, 1], [1, 0.8], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个因素的依次出现 - 在核心观点滑动完成后开始
  const factor1FadeIn = interpolate(currentFrame, [330, 360], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const factor2FadeIn = interpolate(currentFrame, [390, 420], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const factor3FadeIn = interpolate(currentFrame, [450, 480], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 例子动画 - 先居中显示
  const exampleFadeIn = interpolate(currentFrame, [510, 560], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const exampleScale = interpolate(currentFrame, [510, 560], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 例子上滑动画 - 为结论让出位置
  const exampleSlideUp = interpolate(currentFrame, [600, 630], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 例子上滑的位置变化
  const exampleTranslateY = interpolate(exampleSlideUp, [0, 1], [0, -5], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个要点在例子出现时淡出
  const factorsFadeOut = interpolate(currentFrame, [510, 540], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 结论动画 - 在例子上滑后出现
  const conclusionFadeIn = interpolate(currentFrame, [630, 660], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const conclusionScale = interpolate(currentFrame, [630, 660], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="bg-black flex flex-col items-center justify-center text-white relative overflow-hidden">
      <div className="absolute inset-0 opacity-60">
        <Aurora
          colorStops={["#5227FF", "#7cff67", "#ff6b6b", "#4ecdc4"]}
          amplitude={1.2}
          blend={0.7}
          speed={0.8}
        />
      </div>

      {/* 主标题 - 先居中显示，然后滑动到上方 */}
      <div
        className="absolute z-20 w-full h-full flex justify-center items-center"
        style={{
          opacity: titleFadeOut,
          transform: `translate(${titleTranslateX}vw, ${titleTranslateY}vh) scale(${titleScale})`,
          transformOrigin: 'center center',
          transition: 'none',
        }}
      >
        <div className="text-center max-w-4xl px-8">
          <TextType
            text="为什么说「早睡改命」是伪命题"
            className="text-6xl md:text-8xl lg:text-9xl font-semibold text-center leading-tight"
            useFrameBasedAnimation={true}
            startFrame={0}
            duration={90}
            showCursor={true}
            loop={false}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full">

        {/* 核心观点 - 先居中显示，然后滑动到上方 */}
        <div
          className="absolute z-15 w-full h-full flex justify-center items-center"
          style={{
            opacity: corePointCenterPhase * factorsFadeOut,
            transform: `translate(0vw, ${corePointTranslateY}vh) scale(${corePointScale})`,
            transformOrigin: 'center center',
            transition: 'none',
          }}
        >
          <div className="text-center max-w-6xl px-4 overflow-visible">
            <div className="text-4xl md:text-5xl lg:text-6xl text-red-300 font-medium mb-4">
              把"早睡"当成改变命运的钥匙
            </div>
            <TextType
              text="就忽略了其他重要因素——"
              className="text-xl md:text-5xl text-gray-300"
              useFrameBasedAnimation={true}
              startFrame={180}
              duration={80}
              showCursor={true}
              loop={false}
            />
          </div>
        </div>

        {/* 三个被忽略的因素 */}
        <div
          className="flex-1 flex flex-col px-16 space-y-6"
          style={{
            marginTop: '40vh',
            opacity: factorsFadeOut
          }}
        >
          {/* 因素1：睡眠质量 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-red-400/30"
            style={{ opacity: factor1FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-red-200">
                1、睡眠质量：是否深度睡眠足够
              </p>
            </div>
          </div>

          {/* 因素2：睡眠总时长 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-orange-400/30"
            style={{ opacity: factor2FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-orange-200">
                2、睡眠总时长：补觉不能完全抵消熬夜
              </p>
            </div>
          </div>

          {/* 因素3：生活方式 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30"
            style={{ opacity: factor3FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-yellow-200">
                3、生活方式：饮食、运动、压力管理同样关键
              </p>
            </div>
          </div>
        </div>

        {/* 例子和结论的组合显示区域 */}
        <div className="absolute inset-0 flex flex-col items-center justify-center px-8 z-30">
          {/* 例子说明 - 先居中显示，然后上滑 */}
          <div
            className="mb-8"
            style={{
              opacity: exampleFadeIn,
              transform: `translateY(${exampleTranslateY}vh) scale(${exampleScale})`,
              transition: 'none',
            }}
          >
            <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-8 border border-purple-400/30 max-w-4xl">
              <p className="text-5xl md:text-5xl font-semibold text-white text-center">
                如果你早睡，但每天都被打断、睡眠浅，依旧会疲惫
              </p>
            </div>
          </div>

          {/* 结论 - 在例子上滑后出现在下方 */}
          <div
            style={{
              opacity: conclusionFadeIn,
              transform: `scale(${conclusionScale})`,
            }}
          >
            <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-8 border border-red-400/30 max-w-4xl">
              <p className="text-5xl md:text-5xl font-semibold text-white text-center">
                所以，早睡不是"改命"，而是健康生活的一部分
              </p>
            </div>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default MythBustingScene;