import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import TextType from '../components/reactbits/textanimations/TextType';
import GradientText from '../components/reactbits/textanimations/GradientText';
import FuzzyText from '../components/reactbits/textanimations/FuzzyText';
import Aurora from '../components/reactbits/backgrounds/Aurora';

const ConclusionScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 主标题滑动到左上角的动画 (90-150帧)
  const titleSlidePhase = interpolate(currentFrame, [90, 150], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 标题位置和大小变化
  const titleTranslateX = interpolate(titleSlidePhase, [0, 1], [0, -45], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleTranslateY = interpolate(titleSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const titleScale = interpolate(titleSlidePhase, [0, 1], [1, 0.4], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 标题淡出动画 (280帧开始淡出)
  const titleFadeOut = interpolate(currentFrame, [280, 300], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点动画 - 先居中显示，然后滑动到上方
  const corePointCenterPhase = interpolate(currentFrame, [150, 240], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点滑动到上方的动画 (240-300帧)
  const corePointSlidePhase = interpolate(currentFrame, [240, 300], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 核心观点位置和大小变化
  const corePointTranslateY = interpolate(corePointSlidePhase, [0, 1], [0, -35], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const corePointScale = interpolate(corePointSlidePhase, [0, 1], [1, 0.8], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 两个答案的依次出现 - 在核心观点滑动完成后开始
  const answer1FadeIn = interpolate(currentFrame, [330, 360], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const answer2FadeIn = interpolate(currentFrame, [390, 420], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 最终升华动画
  const finalMessageFadeIn = interpolate(currentFrame, [480, 510], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const finalMessageScale = interpolate(currentFrame, [480, 510], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 答案在最终信息出现时淡出
  const answersFadeOut = interpolate(currentFrame, [480, 510], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 第一段文本动画 (510-600帧)
  const firstTextFadeIn = interpolate(currentFrame, [510, 540], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const firstTextFadeOut = interpolate(currentFrame, [600, 630], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 第二段文本动画 (630-720帧)
  const secondTextFadeIn = interpolate(currentFrame, [630, 660], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="bg-black flex flex-col items-center justify-center text-white relative overflow-hidden">
      <div className="absolute inset-0 opacity-60">
        <Aurora
          colorStops={["#5227FF", "#7cff67", "#ff6b6b", "#4ecdc4"]}
          amplitude={1.2}
          blend={0.7}
          speed={0.8}
        />
      </div>

      {/* 主标题 - 先居中显示，然后滑动到上方 */}
      <div
        className="absolute z-20 w-full h-full flex justify-center items-center"
        style={{
          opacity: titleFadeOut,
          transform: `translate(${titleTranslateX}vw, ${titleTranslateY}vh) scale(${titleScale})`,
          transformOrigin: 'center center',
          transition: 'none',
        }}
      >
        <div className="text-center max-w-4xl px-8">
          <TextType
            text="「早睡是伪命题吗？」"
            className="text-6xl md:text-8xl lg:text-9xl font-semibold text-center leading-tight"
            useFrameBasedAnimation={true}
            startFrame={0}
            duration={90}
            showCursor={true}
            loop={false}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full">

        {/* 核心观点 - 先居中显示，然后滑动到上方 */}
        <div
          className="absolute z-15 w-full h-full flex justify-center items-center"
          style={{
            opacity: corePointCenterPhase * answersFadeOut,
            transform: `translate(0vw, ${corePointTranslateY}vh) scale(${corePointScale})`,
            transformOrigin: 'center center',
            transition: 'none',
          }}
        >
          <div className="text-center max-w-6xl px-4 overflow-visible">
            <div className="text-4xl md:text-5xl lg:text-6xl text-yellow-300 font-medium mb-4">
              这取决于你如何理解"早睡"
            </div>
            <TextType
              text="让我们来看两种不同的答案——"
              className="text-xl md:text-5xl text-gray-300"
              useFrameBasedAnimation={true}
              startFrame={180}
              duration={80}
              showCursor={true}
              loop={false}
            />
          </div>
        </div>

        {/* 两个答案 */}
        <div
          className="flex-1 flex flex-col px-16 space-y-6"
          style={{
            marginTop: '40vh',
            opacity: answersFadeOut
          }}
        >
          {/* 第一个答案 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-red-400/30"
            style={{ opacity: answer1FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-red-200">
                1、如果把它当成万能钥匙，它确实是伪命题
              </p>
            </div>
          </div>

          {/* 第二个答案 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-green-400/30"
            style={{ opacity: answer2FadeIn }}
          >
            <div className="flex items-center">
              <p className="text-6xl md:text-6xl font-semibold text-green-200">
                2、但如果理解为保持规律作息，那它就是改善生活的起点
              </p>
            </div>
          </div>
        </div>

        {/* 最终升华信息 - 居中显示 */}
        <div
          className="absolute inset-0 flex items-center justify-center px-8 z-30"
          style={{
            opacity: finalMessageFadeIn,
            transform: `scale(${finalMessageScale})`,
          }}
        >
          <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-8 border border-purple-400/30 max-w-5xl">
            <div className="text-center">
              <GradientText
                className="text-4xl md:text-5xl font-bold mb-6"
                colors={["#ffffff", "#c084fc", "#f9a8d4"]}
                animationSpeed={6}
              >
                健康的睡眠
              </GradientText>
              <div className="text-2xl md:text-3xl text-gray-200 mb-4 text-center relative">
                {/* 第一段文本 */}
                <div
                  className="absolute inset-0"
                  style={{ opacity: firstTextFadeIn * firstTextFadeOut }}
                >
                  <TextType
                    text="不是简单的「早睡」二字"
                    className="text-gray-200 text-center"
                    useFrameBasedAnimation={true}
                    startFrame={510}
                    duration={60}
                    showCursor={false}
                  />
                </div>

                {/* 第二段文本 */}
                <div
                  className="absolute inset-0"
                  style={{ opacity: secondTextFadeIn }}
                >
                  <TextType
                    text="而是规律、充足、高质量的休息"
                    className="text-gray-200 text-center"
                    useFrameBasedAnimation={true}
                    startFrame={600}
                    duration={60}
                    showCursor={false}
                  />
                </div>

                {/* 占位元素保持高度 */}
                <div className="invisible">
                  而是规律、充足、高质量的休息
                </div>
              </div>
              <div className="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mx-auto mb-6"></div>
              <div className="text-xl md:text-2xl text-purple-200 italic">
                <FuzzyText
                  fontSize="clamp(1.25rem, 3vw, 2rem)"
                  color="#ddd6fe"
                  enableHover={false}
                  baseIntensity={0.08}
                >
                  真正的改变，来自于对生活的全面理解和持续改善
                </FuzzyText>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default ConclusionScene;